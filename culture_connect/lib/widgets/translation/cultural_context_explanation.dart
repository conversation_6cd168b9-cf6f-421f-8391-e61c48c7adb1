import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:culture_connect/models/translation/cultural_context_model.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget that displays an explanation for a cultural context note
class CulturalContextExplanation extends StatelessWidget {
  /// The cultural context note to explain
  final CulturalContextNote note;

  /// Whether to animate the widget
  final bool animate;

  /// Creates a new cultural context explanation
  const CulturalContextExplanation({
    super.key,
    required this.note,
    this.animate = true,
  });

  @override
  Widget build(BuildContext context) {
    // Determine text direction based on the context
    final textDirection = Directionality.of(context);
    final isRTL = textDirection == TextDirection.rtl;
    final textAlign = isRTL ? TextAlign.right : TextAlign.left;

    final explanation = Container(
      width: double.infinity,
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: note.type.colorithAlpha(13),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: note.type.colorithAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment:
            isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              // Type badge
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: note.type.colorithAlpha(25),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      note.type.icon,
                      size: 16,
                      color: note.type.color,
                    ),
                    SizedBox(width: 4),
                    Text(
                      note.type.displayName,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: note.type.color,
                      ),
                    ),
                  ],
                ),
              ),

              // Region badge (if available)
              if (noteegion != null) ...[
                SizedBox(width: 8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    noteegion!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
              ],

              // Formality level badge (if available)
              if (note.formalityLevel != null) ...[
                SizedBox(width: 8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color:
                        _getFormalityColor(note.formalityLevel!)ithAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    note.formalityLevel!,
                    style: TextStyle(
                      fontSize: 12,
                      color: _getFormalityColor(note.formalityLevel!),
                    ),
                  ),
                ),
              ],
            ],
          ),

          SizedBox(height: 12),

          // Text segment
          if (note.textSegment.isNotEmpty) ...[
            Text(
              'Original Text:',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
              textAlign: textAlign,
            ),
            SizedBox(height: 4),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colorshite,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
              ),
              child: Text(
                '"${note.textSegment}"',
                style: TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: Colors.black87,
                ),
                textAlign: textAlign,
              ),
            ),
            SizedBox(height: 12),
          ],

          // Explanation
          Text(
            'Explanation:',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
            textAlign: textAlign,
          ),
          SizedBox(height: 4),
          Text(
            note.explanation,
            style: TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
            textAlign: textAlign,
          ),

          // Alternatives (if available)
          if (note.alternatives != null && note.alternatives!.isNotEmpty) ...[
            SizedBox(height: 12),
            Text(
              'Alternative Expressions:',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
              textAlign: textAlign,
            ),
            SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: note.alternatives!.map((alternative) {
                return Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppTheme.primaryColor.withAlpha(76),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    alternative,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],

          // Resources (if available)
          if (noteesources != null && noteesources!.isNotEmpty) ...[
            SizedBox(height: 16),
            Text(
              'Learn More:',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
              textAlign: textAlign,
            ),
            SizedBox(height: 8),
            ...noteesources!.map((resource) {
              return Padding(
                padding: EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.link,
                      size: 16,
                      color: AppTheme.primaryColor,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        resource,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.primaryColor,
                          decoration: TextDecoration.underline,
                        ),
                        textAlign: textAlign,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ],
      ),
    );

    if (animate) {
      return explanation.animate().fadeIn(duration: 300.ms).slideY(
          begin: 0.1, end: 0, duration: 300.ms, curve: Curves.easeOutQuad);
    }

    return explanation;
  }

  /// Get the color for a formality level
  Color _getFormalityColor(String formalityLevel) {
    switch (formalityLevel.toLowerCase()) {
      case 'formal':
      case 'very formal':
        return Colors.indigo;
      case 'informal':
      case 'very informal':
        return Colors.orange;
      case 'neutral':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }
}
