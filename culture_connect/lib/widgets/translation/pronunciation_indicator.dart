import 'package:flutter/material.dart';
import 'package:culture_connect/models/translation/pronunciation_model.dart';
import 'package:culture_connect/models/translation/translation_pronunciation.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for displaying a pronunciation indicator
class PronunciationIndicator extends StatelessWidget {
  /// The pronunciation information
  final TranslationPronunciation pronunciation;

  /// Whether to show the label
  final bool showLabel;

  /// Whether to use a compact layout
  final bool compact;

  /// Whether to use light colors (for dark backgrounds)
  final bool useLight;

  /// Callback when the indicator is tapped
  final VoidCallback? onTap;

  /// Creates a new pronunciation indicator
  const PronunciationIndicator({
    super.key,
    required this.pronunciation,
    this.showLabel = true,
    this.compact = false,
    this.useLight = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (!pronunciationasPronunciationGuides) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon
          Icon(
            Iconsecord_voice_over,
            size: compact ? 14 : 18,
            color: useLight ? Colorshite70 : Colors.teal,
          ),

          if (showLabel) ...[
            SizedBox(width: 4),

            // Label
            Text(
              compact ? 'Pronunciation' : 'Pronunciation Guide',
              style: TextStyle(
                fontSize: compact ? 10 : 12,
                color: useLight ? Colorshite70 : AppTheme.textSecondaryColor,
              ),
            ),
          ],

          // Badge with count
          if (pronunciation.guideCount > 0) ...[
            SizedBox(width: 4),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: useLight ? Colorshite24 : Colors.tealithAlpha(51),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                pronunciation.guideCount.toString(),
                style: TextStyle(
                  fontSize: compact ? 8 : 10,
                  fontWeight: FontWeight.bold,
                  color: useLight ? Colorshite : Colors.teal,
                ),
              ),
            ),
          ],

          // Difficult badge
          if (pronunciation.hasDifficultGuides) ...[
            SizedBox(width: 4),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: useLight
                    ? Colors.white.withAlpha(61)
                    : Colors.orange.withAlpha(51),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.priority_high,
                size: compact ? 10 : 12,
                color: useLight ? Colors.white : Colors.orange,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// A widget for displaying a pronunciation guide
class PronunciationGuideCard extends StatelessWidget {
  /// The pronunciation guide
  final PronunciationGuide guide;

  /// Callback when the audio button is tapped
  final Function(String)? onPlayAudio;

  /// Creates a new pronunciation guide card
  const PronunciationGuideCard({
    super.key,
    required this.guide,
    this.onPlayAudio,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: guide.difficulty.color.withAlpha(77),
          width: 1,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: guide.difficulty.color.withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    guide.difficulty.icon,
                    size: 20,
                    color: guide.difficulty.color,
                  ),
                ),
                SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      guide.text,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    SizedBox(height: 2),
                    Text(
                      '${guide.difficulty.displayName} Difficulty',
                      style: TextStyle(
                        fontSize: 12,
                        color: guide.difficulty.color,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                if (guide.audioPath != null)
                  IconButton(
                    icon: const Icon(Icons.volume_up),
                    color: Colors.teal,
                    onPressed: () {
                      if (onPlayAudio != null) {
                        onPlayAudio!(guide.audioPath!);
                      }
                    },
                  ),
              ],
            ),

            SizedBox(height: 16),

            // Pronunciation
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        guide.type.icon,
                        size: 16,
                        color: Colors.teal,
                      ),
                      SizedBox(width: 8),
                      Text(
                        '${guide.type.displayName} Pronunciation',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.teal,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    guide.pronunciation,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                      fontFamily: guide.type == PronunciationGuideType.ipa
                          ? 'NotoSans'
                          : null,
                    ),
                  ),
                ],
              ),
            ),

            // Tips (if available)
            if (guide.tips != null && guide.tips!.isNotEmpty) ...[
              SizedBox(height: 16),
              Text(
                'Pronunciation Tips',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              SizedBox(height: 8),
              ...guide.tips!.map((tip) {
                return Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.tips_and_updates_outlined,
                        size: 16,
                        color: Colors.amber,
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          tip,
                          style: TextStyle(
                            fontSize: 14,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],

            // Common mistakes (if available)
            if (guide.commonMistakes != null &&
                guide.commonMistakes!.isNotEmpty) ...[
              SizedBox(height: 16),
              Text(
                'Common Mistakes',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              SizedBox(height: 8),
              ...guide.commonMistakes!.map((mistake) {
                return Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 16,
                        color: Colorsed,
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          mistake,
                          style: TextStyle(
                            fontSize: 14,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],

            // Additional information
            if (guideegion != null) ...[
              SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.location_on_outlined,
                    size: 16,
                    color: Colors.green,
                  ),
                  SizedBox(width: 8),
                  Text(
                    '${guideegion} dialect',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
