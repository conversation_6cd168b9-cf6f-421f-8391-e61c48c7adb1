import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/flight/flight_info.dart';
import 'package:culture_connect/services/travel/transfer/flight_integration_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/custom_text_field.dart';
import 'package:culture_connect/widgets/common/custom_date_picker.dart';

/// A form for entering flight information
class FlightInfoForm extends StatefulWidget {
  /// The initial flight number
  final String? initialFlightNumber;

  /// The initial flight date
  final DateTime? initialFlightDate;

  /// Callback when the form is submitted
  final Function(String flightNumber, DateTime flightDate) onSubmit;

  /// Callback when flight information is loaded
  final Function(FlightInfo flightInfo)? onFlightInfoLoaded;

  /// Creates a new flight info form
  const FlightInfoForm({
    super.key,
    this.initialFlightNumber,
    this.initialFlightDate,
    required this.onSubmit,
    this.onFlightInfoLoaded,
  });

  @override
  State<FlightInfoForm> createState() => _FlightInfoFormState();
}

class _FlightInfoFormState extends State<FlightInfoForm> {
  final _formKey = GlobalKey<FormState>();
  final _flightNumberController = TextEditingController();
  late DateTime _flightDate;

  bool _isLoading = false;
  FlightInfo? _flightInfo;
  String? _errorMessage;

  final _flightService = FlightIntegrationService();

  @override
  void initState() {
    super.initState();
    _flightNumberController.text = widget.initialFlightNumber ?? '';
    _flightDate =
        widget.initialFlightDate ?? DateTime.now().add(const Duration(days: 1));

    // Load flight info if initial values are provided
    if (widget.initialFlightNumber != null &&
        widget.initialFlightDate != null) {
      _loadFlightInfo();
    }
  }

  @override
  void dispose() {
    _flightNumberController.dispose();
    super.dispose();
  }

  /// Load flight information
  Future<void> _loadFlightInfo() async {
    if (_flightNumberController.text.isEmpty) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final flightInfo = await _flightService.getFlightInfo(
        _flightNumberController.text,
        _flightDate,
      );

      setState(() {
        _flightInfo = flightInfo;
        _isLoading = false;
      });

      if (flightInfo != null && widget.onFlightInfoLoaded != null) {
        widget.onFlightInfoLoaded!(flightInfo);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading flight information';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Flight Information',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),

          // Flight number
          CustomTextField(
            controller: _flightNumberController,
            label: 'Flight Number',
            hint: 'e.g. BA123',
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a flight number';
              }
              // Simple validation for flight number format
              if (!RegExp(r'^[A-Z0-9]{2,3}[0-9]{1,4}$')asMatch(value)) {
                return 'Please enter a valid flight number (e.g. BA123)';
              }
              return null;
            },
          ),

          SizedBox(height: 16),

          // Flight date
          CustomDatePicker(
            labelText: 'Flight Date',
            initialDate: _flightDate,
            firstDate: DateTime.now().subtract(const Duration(days: 1)),
            lastDate: DateTime.now().add(const Duration(days: 365)),
            onDateSelected: (date) {
              setState(() {
                _flightDate = date;
              });
            },
          ),

          SizedBox(height: 16),

          // Search button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _loadFlightInfo,
              icon: _isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: const CircularProgressIndicator(
                        color: Colorshite,
                        strokeWidth: 2,
                      ),
                    )
                  : const Icon(Icons.search),
              label: Text(_isLoading ? 'Searching...' : 'Search Flight'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colorshite,
                padding: EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),

          // Error message
          if (_errorMessage != null) ...[
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colorsed[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colorsed[300]!),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error,
                    color: Colorsed,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colorsed,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Flight info
          if (_flightInfo != null) ...[
            SizedBox(height: 24),
            _buildFlightInfoCard(),
          ],

          SizedBox(height: 16),

          // Submit button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  widget.onSubmit(_flightNumberController.text, _flightDate);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colorshite,
                padding: EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Confirm Flight Details'),
            ),
          ),
        ],
      ),
    );
  }

  /// Build the flight info card
  Widget _buildFlightInfoCard() {
    final flight = _flightInfo!;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Airline and flight number
            Row(
              children: [
                Icon(
                  Icons.flight,
                  size: 24,
                  color: Colors.blue,
                ),
                SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      flight.airlineName,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Flight ${flight.flightNumber}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(flight.status.displayName)
                        ithAlpha(30),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    flight.status.displayName,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: _getStatusColor(flight.status.displayName),
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: 16),

            // Flight route
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        flight.departureAirportCode,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        flight.departureCity,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      if (flight.departureTerminal != null) ...[
                        SizedBox(height: 4),
                        Text(
                          flight.departureTerminal!,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Column(
                  children: [
                    Icon(
                      Icons.flight_takeoff,
                      size: 24,
                      color: Colors.grey[600],
                    ),
                    SizedBox(height: 4),
                    Container(
                      width: 60,
                      height: 1,
                      color: Colors.grey[300],
                    ),
                  ],
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        flight.arrivalAirportCode,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        flight.arrivalCity,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      if (flight.arrivalTerminal != null) ...[
                        SizedBox(height: 4),
                        Text(
                          flight.arrivalTerminal!,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 16),

            // Flight times
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Departure',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        _formatDateTime(flight.scheduledDepartureTime),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (flight.actualDepartureTime != null) ...[
                        SizedBox(height: 4),
                        Text(
                          'Actual: ${_formatDateTime(flight.actualDepartureTime!)}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Arrival',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        _formatDateTime(flight.scheduledArrival),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (flight.actualArrival != null) ...[
                        SizedBox(height: 4),
                        Text(
                          'Actual: ${_formatDateTime(flight.actualArrival!)}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Format a date and time
  String _formatDateTime(DateTime dateTime) {
    final hour = dateTime.hour > 12
        ? dateTime.hour - 12
        : dateTime.hour == 0
            ? 12
            : dateTime.hour;
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = dateTime.hour >= 12 ? 'PM' : 'AM';
    return '${dateTime.month}/${dateTime.day} $hour:$minute $period';
  }

  /// Get the color for a flight status
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'scheduled':
      case 'on time':
        return Colors.green;
      case 'delayed':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      case 'boarding':
        return Colors.blue;
      case 'departed':
      case 'in air':
        return Colors.purple;
      case 'arrived':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }
}
